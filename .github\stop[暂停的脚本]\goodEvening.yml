name: goodEvening for you
on:
  workflow_dispatch:
    branches:
      - master
  schedule:
    # `分 时 天 月 周` 时按照标准时间 北京时间=标准时间+8 18表示北京时间早上2点
    # 晚上 11：20
    - cron: '20 15 * * *'
# on:
#   push:
#     branches:
#       - master

jobs:
  build:
    runs-on: ubuntu-latest

    steps:
      - uses: actions/checkout@v2

      # - name: Use Node@14.x # 步骤2
      #   uses: actions/setup-node@v1 # 作用：安装nodejs
      #   with:
      #     node-version: 14

      - name: Install pnpm
        uses: pnpm/action-setup@v2.0.1
        with:
          version: 6.15.1

      - name: Set node version to ${{ matrix.node_version }}
        uses: actions/setup-node@v2
        with:
          node-version: ${{ matrix.node_version }}
          cache: 'pnpm'

      - name: Install
        run: pnpm install

      # - name: Build
      #   run: pnpm build

      # 运行脚本
      - name: 运行脚本
        run: pnpm start
        env:
          WX_COMPANY_ID: ${{ secrets.WX_COMPANY_ID }}
          WX_APP_ID: ${{ secrets.WX_APP_ID }}
          WX_APP_SECRET: ${{ secrets.WX_APP_SECRET }}
          TIAN_API_KEY: ${{ secrets.TIAN_API_KEY }}
          MESSAGE_TYPE: goodEvening
