{"name": "notify-server", "version": "0.0.1", "description": "使用TypeScript运行和开发的nodejs技术栈的微信消息通知项目", "keywords": ["女朋友", "微信", "微信通知", "土味情话", "天气", "早安"], "homepage": "https://github.com/JS-banana/node-ts#readme", "bugs": {"url": "https://github.com/JS-banana/node-ts/issues"}, "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/JS-banana/node-ts.git"}, "author": "JS-banana <<EMAIL>>", "sideEffects": false, "files": ["dist"], "exports": {".": {"require": "./dist/index.cjs", "import": "./dist/index.mjs", "types": "./dist/index.d.ts"}}, "main": "./dist/index.cjs", "module": "./dist/index.mjs", "types": "./dist/index.d.ts", "scripts": {"build": "unbuild", "dev": "unbuild --stub", "lint": "eslint --ext .js,.ts .", "prepublishOnly": "npm run build", "release": "bumpp --commit --push --tag && npm publish", "start": "esno src/index.ts", "test": "vitest"}, "dependencies": {"axios": "^1.6.7", "dayjs": "^1.11.10", "dotenv": "^16.4.5", "yaml": "^2.3.4"}, "devDependencies": {"@antfu/eslint-config": "^2.10.0", "@antfu/ni": "^0.20.0", "@types/node": "^20.11.19", "bumpp": "^9.0.0", "eslint": "^8.56.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.1.3", "esno": "^0.16.3", "prettier": "^3.2.5", "typescript": "^5.3.3", "unbuild": "^2.0.0", "vite": "^5.1.3", "vitest": "^1.2.2"}}