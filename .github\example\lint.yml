name: Lint

on:
  push:
    branches:
      - main

  pull_request:
    branches:
      - main

jobs:
  build:
    runs-on: ubuntu-latest

    steps:
      - uses: actions/checkout@v2

      - name: Install pnpm
        uses: pnpm/action-setup@v2.0.1
        with:
          version: 6.15.1

      - name: Set node
        uses: actions/setup-node@v2
        with:
          node-version: 16
          cache: 'pnpm'

      - name: Setup
        run: npm i -g @antfu/ni

      - name: Install
        run: nci

      - name: Lint
        run: nr lint
