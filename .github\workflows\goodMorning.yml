name: GoodMorning

on:
  workflow_dispatch:
    branches:
      - main
  schedule:
    # `分 时 天 月 周` 标准时间，北京+8
    - cron: '30 2 * * *' # 北京时间 10:30AM

jobs:
  build:
    runs-on: ubuntu-latest

    steps:
      # 1. 拉代码
      - uses: actions/checkout@v2

      # 2. 设置Node环境（直接指定 node 版本，不搞 matrix）
      - name: Set node version
        uses: actions/setup-node@v2
        with:
          node-version: '20' # 你需要啥版本，自己改

      # 3. 安装依赖
      - name: Install
        run: npm install

      # 4. 运行脚本
      - name: Run Script
        run: npm start
        env:
          WX_COMPANY_ID: ${{ secrets.WX_COMPANY_ID }}
          WX_APP_ID: ${{ secrets.WX_APP_ID }}
          WX_APP_SECRET: ${{ secrets.WX_APP_SECRET }}
          TIAN_API_KEY: ${{ secrets.TIAN_API_KEY }}
          MESSAGE_TYPE: goodMorning
