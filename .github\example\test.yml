name: Test

on:
  push:
    branches:
      - main

  pull_request:
    branches:
      - main

jobs:
  build:
    runs-on: ${{ matrix.os }}

    strategy:
      matrix:
        node-version: [14.x, 16.x]
        os: [ubuntu-latest, windows-latest, macos-latest]
      fail-fast: false

    steps:
      - uses: actions/checkout@v2

      - name: Install pnpm
        uses: pnpm/action-setup@v2.0.1
        with:
          version: 6.15.1

      - name: Set node version to ${{ matrix.node_version }}
        uses: actions/setup-node@v2
        with:
          node-version: ${{ matrix.node_version }}
          cache: "pnpm"

      - name: Setup
        run: npm i -g @antfu/ni

      - name: Install
        run: nci

      - name: Install
        run: nr build

      - name: Test
        run: nr test --if-present
